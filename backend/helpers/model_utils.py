"""
Model utility functions for automatic model upgrading and management.
"""

from helpers.LLM_Handler import LLM_Handler


def upgrade_model_to_level_2(model, default_gemini_model="gemini-2.0-flash", default_gpt_model="gpt-5-mini"):
    """
    Automatically upgrade level 1 models to level 2 when level 2 is expected.
    
    Args:
        model (str): The model to potentially upgrade
        default_gemini_model (str): The default level 1 Gemini model to upgrade from
        default_gpt_model (str): The default level 1 GPT model to upgrade from
        
    Returns:
        str: The upgraded model or original model if no upgrade needed
    """
    if model == default_gemini_model or model == LLM_Handler.latest_small_gemini_model():
        return LLM_Handler.latest_medium_gemini_model()
    elif model == default_gpt_model or model == LLM_Handler.latest_small_openai_model():
        return LLM_Handler.latest_medium_openai_model()
    else:
        return model
    
def upgrade_model_to_level_3(model, default_gemini_model="gemini-2.0-flash", default_gpt_model="gpt-5-mini"):
    """
    Automatically upgrade level 1 or 2 models to level 3 when level 3 is expected.
    
    Args:
        model (str): The model to potentially upgrade
        default_gemini_model (str): The default level 1 Gemini model to upgrade from
        default_gpt_model (str): The default level 1 GPT model to upgrade from
        
    Returns:
        str: The upgraded model or original model if no upgrade needed
    """
    if model == default_gemini_model or model == LLM_Handler.latest_small_gemini_model() or model==LLM_Handler.latest_medium_gemini_model():
        return LLM_Handler.latest_large_gemini_model()
    elif model == default_gpt_model or model == LLM_Handler.latest_small_openai_model() or model==LLM_Handler.latest_medium_openai_model():
        return LLM_Handler.latest_large_openai_model()
    else:
        return model

def upgrade_model_by_one(model, default_gemini_model="gemini-2.0-flash", default_gpt_model="gpt-5-mini"):
    """
    Automatically upgrade models to the next level when a higher level is expected.

    Args:
        model (str): The model to potentially upgrade
        default_gemini_model (str): The default level 1 Gemini model to upgrade from
        default_gpt_model (str): The default level 1 GPT model to upgrade from

    Returns:
        str: The upgraded model or original model if no upgrade needed
    """
    if model in [default_gemini_model, LLM_Handler.latest_small_gemini_model(), LLM_Handler.latest_small_openai_model(), default_gpt_model]:
        return upgrade_model_to_level_2(model, default_gemini_model, default_gpt_model)
    elif model in [LLM_Handler.latest_medium_gemini_model(), LLM_Handler.latest_medium_openai_model()]:
        return LLM_Handler.latest_large_openai_model()
    else:
        return model